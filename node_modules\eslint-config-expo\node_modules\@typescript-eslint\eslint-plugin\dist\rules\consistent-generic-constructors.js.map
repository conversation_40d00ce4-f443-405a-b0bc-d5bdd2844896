{"version": 3, "file": "consistent-generic-constructors.js", "sourceRoot": "", "sources": ["../../src/rules/consistent-generic-constructors.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAE1D,kCAAoE;AAKpE,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,iCAAiC;IACvC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,wGAAwG;YAC1G,WAAW,EAAE,WAAW;SACzB;QACD,QAAQ,EAAE;YACR,oBAAoB,EAClB,gFAAgF;YAClF,iBAAiB,EACf,2FAA2F;SAC9F;QACD,OAAO,EAAE,MAAM;QACf,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,CAAC,iBAAiB,EAAE,aAAa,CAAC;aACzC;SACF;KACF;IACD,cAAc,EAAE,CAAC,aAAa,CAAC;IAC/B,MAAM,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC;QACpB,OAAO;YACL,4GAA4G,CAC1G,IAG+B;gBAE/B,SAAS,SAAS;oBAIhB,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;wBAClB,KAAK,sBAAc,CAAC,kBAAkB;4BACpC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;wBAC9B,KAAK,sBAAc,CAAC,kBAAkB;4BACpC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;wBAC5B,KAAK,sBAAc,CAAC,iBAAiB;4BACnC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;wBACjC;4BACE,MAAM,IAAI,KAAK,CACb,wBAAyB,IAAyB,CAAC,IAAI,EAAE,CAC1D,CAAC;oBACN,CAAC;gBACH,CAAC;gBACD,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,SAAS,EAAE,CAAC;gBACnC,MAAM,GAAG,GAAG,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;gBAEnD,IACE,CAAC,GAAG;oBACJ,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa;oBACzC,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAC7C,CAAC;oBACD,OAAO;gBACT,CAAC;gBACD,IACE,GAAG;oBACH,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;wBAC1C,GAAG,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;wBAC/C,GAAG,CAAC,QAAQ,CAAC,IAAI,KAAK,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EACxC,CAAC;oBACD,OAAO;gBACT,CAAC;gBACD,IAAI,IAAI,KAAK,iBAAiB,EAAE,CAAC;oBAC/B,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,aAAa,EAAE,CAAC;wBAC9B,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC;wBACtC,MAAM,cAAc,GAClB,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC;4BAClC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;wBAC5C,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI;4BACJ,SAAS,EAAE,sBAAsB;4BACjC,GAAG,CAAC,KAAK;gCACP,SAAS,uBAAuB;oCAG9B,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAAE,CAAC;wCACpD,OAAO,OAAO,CAAC;oCACjB,CAAC;oCACD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;wCACnB,OAAO,IAAI,CAAC,GAAG,CAAC;oCAClB,CAAC;oCACD,oDAAoD;oCACpD,mEAAmE;oCACnE,OAAO,IAAA,iBAAU,EACf,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,EAC1C,wBAAiB,CAAC,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC,CAC3C,CAAC;gCACJ,CAAC;gCACD,OAAO;oCACL,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC;oCAC3B,KAAK,CAAC,eAAe,CACnB,uBAAuB,EAAE,EACzB,IAAI,GAAG,cAAc,CACtB;iCACF,CAAC;4BACJ,CAAC;yBACF,CAAC,CAAC;oBACL,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,IAAI,GAAG,EAAE,aAAa,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;oBAC7C,MAAM,SAAS,GACb,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,KAAK,KAAK,GAAG,CAAC;oBAC9D,MAAM,aAAa,GAAG,IAAI,GAAG,CAC3B,OAAO,CAAC,UAAU,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,CACjD,CAAC;oBACF,OAAO,CAAC,UAAU;yBACf,iBAAiB,CAAC,GAAG,CAAC,aAAa,CAAC;yBACpC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;oBACzC,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,mBAAmB;wBAC9B,CAAC,GAAG,CAAC,KAAK;4BACR,MAAM,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;4BAC/B,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE,CAAC;gCACpC,MAAM,KAAK,CAAC,eAAe,CACzB,GAAG,CAAC,MAAM,EACV,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CACpC,CAAC;4BACJ,CAAC;4BACD,MAAM,KAAK,CAAC,eAAe,CACzB,GAAG,CAAC,MAAM,EACV,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAC9C,CAAC;4BACF,IAAI,CAAC,SAAS,EAAE,CAAC;gCACf,MAAM,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;4BAChD,CAAC;wBACH,CAAC;qBACF,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}