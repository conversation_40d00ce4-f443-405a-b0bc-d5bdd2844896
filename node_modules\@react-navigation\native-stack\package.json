{"name": "@react-navigation/native-stack", "description": "Native stack navigator using react-native-screens", "version": "6.11.0", "keywords": ["react-native-component", "react-component", "react-native", "react-navigation", "ios", "android", "native", "stack"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/react-navigation/react-navigation.git", "directory": "packages/native-stack"}, "bugs": {"url": "https://github.com/software-mansion/react-native-screens/issues"}, "homepage": "https://github.com/software-mansion/react-native-screens#readme", "main": "lib/commonjs/index.js", "react-native": "src/index.tsx", "source": "src/index.tsx", "module": "lib/module/index.js", "types": "lib/typescript/src/index.d.ts", "files": ["src", "lib", "!**/__tests__"], "sideEffects": false, "publishConfig": {"access": "public"}, "scripts": {"prepack": "bob build", "clean": "del lib"}, "dependencies": {"@react-navigation/elements": "^1.3.31", "warn-once": "^0.1.0"}, "devDependencies": {"@react-navigation/native": "^6.1.18", "@testing-library/react-native": "^11.5.0", "@types/react": "~18.0.27", "@types/react-native": "~0.71.3", "del-cli": "^5.0.0", "react": "18.2.0", "react-native": "0.71.8", "react-native-builder-bob": "^0.20.4", "react-native-screens": "~3.29.0", "typescript": "^4.9.4"}, "peerDependencies": {"@react-navigation/native": "^6.0.0", "react": "*", "react-native": "*", "react-native-safe-area-context": ">= 3.0.0", "react-native-screens": ">= 3.0.0"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", ["typescript", {"project": "tsconfig.build.json"}]]}, "gitHead": "02dd64b51a1ddf6e5a197785df4b5f7927953117"}