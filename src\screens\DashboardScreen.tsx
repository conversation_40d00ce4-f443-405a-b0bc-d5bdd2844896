import React, { useState, useEffect } from 'react';
import { View, ScrollView, Text, RefreshControl, Pressable } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useAdmissionMetrics, useRevenueMetrics, useDischargeMetrics } from '../hooks/useApi';
import { FilterParams } from '../types/api';
import MetricCard from '../components/common/MetricCard';
import LoadingSpinner from '../components/common/LoadingSpinner';
import ErrorMessage from '../components/common/ErrorMessage';
import FilterPanel from '../components/filters/FilterPanel';
import ThemeToggle from '../components/common/ThemeToggle';
import FloatingActionButton from '../components/common/FloatingActionButton';
import { formatCurrency, formatNumber } from '../utils/formatters';
import { useTheme, useThemedStyles } from '../theme';
import { useUiStore } from '../store/uiStore';
import { shadowStyle } from '../theme/shadow';

const DashboardScreen: React.FC = () => {
  const theme = useTheme();
  const { chartAnimations, autoRefresh } = useUiStore();

  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1;
  const currentQuarter = `Q${Math.ceil(currentMonth / 3)}`;

  const [filters, setFilters] = useState<FilterParams>({
    year: currentYear,
    quarter: currentQuarter,
  });

  const [dataLoaded, setDataLoaded] = useState(false);
  const [expandedSection, setExpandedSection] = useState<string | null>(null);

  // Create styles based on theme
  const styles = useThemedStyles((theme) => ({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      backgroundColor: theme.colors.surface,
      padding: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitle: {
      fontSize: theme.typography.fontSizes['2xl'],
      fontWeight: '700',
      color: theme.colors.text,
      marginBottom: theme.spacing.xs,
    },
    headerSubtitle: {
      fontSize: theme.typography.fontSizes.base,
      color: theme.colors.textSecondary,
    },
    sectionHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: theme.spacing.sm,
    },
    sectionTitle: {
      fontSize: theme.typography.fontSizes.lg,
      fontWeight: '600',
      color: theme.colors.text,
    },
    sectionIcon: {
      marginRight: theme.spacing.xs,
    },
    contentContainer: {
      padding: theme.spacing.md,
    },
    sectionContainer: {
      marginBottom: theme.spacing.xl,
    },
    emptyStateContainer: {
      padding: theme.spacing.md,
      backgroundColor: theme.isDark ? theme.colors.primary[900] : theme.colors.primary[50],
      borderRadius: theme.borderRadius.md,
      borderWidth: 1,
      borderColor: theme.isDark ? theme.colors.primary[800] : theme.colors.primary[200],
    },
    emptyStateText: {
      textAlign: 'center',
      color: theme.isDark ? theme.colors.primary[300] : theme.colors.primary[800],
      fontWeight: '500',
    },
  }));

  // API hooks
  const {
    data: admissionMetrics,
    isLoading: admissionsLoading,
    error: admissionsError,
    refetch: refetchAdmissions,
  } = useAdmissionMetrics(filters, dataLoaded);

  const {
    data: revenueMetrics,
    isLoading: revenueLoading,
    error: revenueError,
    refetch: refetchRevenue,
  } = useRevenueMetrics(filters, dataLoaded);

  const {
    data: dischargeMetrics,
    isLoading: dischargesLoading,
    error: dischargesError,
    refetch: refetchDischarges,
  } = useDischargeMetrics(filters, dataLoaded);

  const isLoading = admissionsLoading || revenueLoading || dischargesLoading;
  const hasError = admissionsError || revenueError || dischargesError;

  const handleApplyFilters = () => {
    setDataLoaded(true);
  };

  const handleRefresh = async () => {
    if (dataLoaded) {
      await Promise.all([
        refetchAdmissions(),
        refetchRevenue(),
        refetchDischarges(),
      ]);
    }
  };

  // Auto-load data on first render
  useEffect(() => {
    setDataLoaded(true);
  }, []);

  // Section component for better organization
  const Section = ({
    title,
    icon,
    color,
    children,
    id
  }: {
    title: string;
    icon: keyof typeof Ionicons.glyphMap;
    color: string;
    children: React.ReactNode;
    id: string;
  }) => {
    const isExpanded = expandedSection === id || expandedSection === null;

    return (
      <View style={styles.sectionContainer}>
        <Pressable
          onPress={() => setExpandedSection(isExpanded && expandedSection !== null ? null : id)}
          style={styles.sectionHeader}
        >
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Ionicons
              name={icon}
              size={24}
              color={theme.colors[color as keyof typeof theme.colors][600]}
              style={styles.sectionIcon}
            />
            <Text style={styles.sectionTitle}>{title}</Text>
          </View>
          <Ionicons
            name={isExpanded ? 'chevron-up' : 'chevron-down'}
            size={20}
            color={theme.colors.textSecondary}
          />
        </Pressable>

        {isExpanded && children}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary[500]]}
            tintColor={theme.colors.primary[500]}
            progressBackgroundColor={theme.colors.surface}
          />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: theme.spacing.xs }}>
            <Text style={styles.headerTitle}>
              Hospital Dashboard
            </Text>
            <ThemeToggle size="small" />
          </View>
          <Text style={styles.headerSubtitle}>
            Overview of key hospital metrics and analytics
          </Text>
        </View>

        {/* Filters */}
        <FilterPanel
          filters={filters}
          onFiltersChange={setFilters}
          onApplyFilters={handleApplyFilters}
        />

        {/* Content */}
        {!dataLoaded ? (
          <View style={styles.contentContainer}>
            <View style={styles.emptyStateContainer}>
              <Text style={styles.emptyStateText}>
                Please select filters and apply to view dashboard data
              </Text>
            </View>
          </View>
        ) : isLoading ? (
          <LoadingSpinner text="Loading dashboard data..." fullScreen />
        ) : hasError ? (
          <ErrorMessage
            message="Failed to load dashboard data. Please try again."
            onRetry={handleRefresh}
          />
        ) : (
          <View style={styles.contentContainer}>
            {/* Admissions Section */}
            <Section title="Admissions Overview" icon="people" color="primary" id="admissions">
              <View className="space-y-3">
                <MetricCard
                  title="Total Admissions"
                  value={formatNumber(admissionMetrics?.total_admissions || 0)}
                  delta={`${(admissionMetrics?.average_daily || 0).toFixed(1)} per day`}
                  deltaType="neutral"
                  icon="people"
                  color="primary"
                  onPress={() => {/* Navigate to detailed view */}}
                  subtitle="All hospital admissions in selected period"
                />
                <MetricCard
                  title="Average Daily Admissions"
                  value={(admissionMetrics?.average_daily || 0).toFixed(1)}
                  delta={admissionMetrics?.busiest_day ? `Busiest: ${admissionMetrics.busiest_day}` : undefined}
                  deltaType="neutral"
                  icon="trending-up"
                  color="success"
                  subtitle="Average admissions per day"
                />
              </View>
            </Section>

            {/* Revenue Section */}
            <Section title="Revenue Overview" icon="cash" color="success" id="revenue">
              <View className="space-y-3">
                <MetricCard
                  title="Total Revenue"
                  value={formatCurrency(revenueMetrics?.total_revenue || 0)}
                  delta={`${revenueMetrics?.bill_count || 0} bills`}
                  deltaType="neutral"
                  icon="cash"
                  color="success"
                  onPress={() => {/* Navigate to detailed view */}}
                  subtitle="Total revenue in selected period"
                />
                <MetricCard
                  title="Average Bill Amount"
                  value={formatCurrency(revenueMetrics?.average_bill || 0)}
                  delta={`₹${((revenueMetrics?.average_bill || 0) / 1000).toFixed(1)}K per bill`}
                  deltaType="neutral"
                  icon="card"
                  color="info"
                  subtitle="Average revenue per bill"
                />
                <MetricCard
                  title="Average Daily Revenue"
                  value={formatCurrency(revenueMetrics?.average_daily_revenue || 0)}
                  icon="trending-up"
                  color="success"
                  subtitle="Average revenue generated per day"
                />
              </View>
            </Section>

            {/* Discharges Section */}
            <Section title="Discharges Overview" icon="exit" color="warning" id="discharges">
              <View className="space-y-3">
                <MetricCard
                  title="Total Discharges"
                  value={formatNumber(dischargeMetrics?.total_discharges || 0)}
                  delta={`${(dischargeMetrics?.average_daily || 0).toFixed(1)} per day`}
                  deltaType="neutral"
                  icon="exit"
                  color="warning"
                  onPress={() => {/* Navigate to detailed view */}}
                  subtitle="Total patients discharged in period"
                />
              </View>
            </Section>

            {/* Quick Stats */}
            <Section title="Quick Statistics" icon="stats-chart" color="info" id="stats">
              <View style={{
  backgroundColor: theme.isDark ? theme.colors.gray[800] : theme.colors.surface,
  borderRadius: 12,
  padding: 16,
  borderWidth: 1,
  borderColor: theme.isDark ? theme.colors.gray[700] : theme.colors.gray[200],
  ...shadowStyle,
}}>
                <View className="flex-row justify-between items-center mb-3">
                  <View className="flex-row items-center">
                    <Ionicons name="people-circle" size={20} color={theme.colors.primary[500]} style={{marginRight: 8}} />
                    <Text style={{color: theme.colors.textSecondary}}>Admission Types</Text>
                  </View>
                  <Text style={{fontWeight: '600', color: theme.colors.text}}>
                    {admissionMetrics?.admission_type_distribution?.length || 0} types
                  </Text>
                </View>
                <View className="flex-row justify-between items-center mb-3">
                  <View className="flex-row items-center">
                    <Ionicons name="layers" size={20} color={theme.colors.success[500]} style={{marginRight: 8}} />
                    <Text style={{color: theme.colors.textSecondary}}>Service Groups</Text>
                  </View>
                  <Text style={{fontWeight: '600', color: theme.colors.text}}>
                    {revenueMetrics?.service_group_distribution?.length || 0} groups
                  </Text>
                </View>
                <View className="flex-row justify-between items-center">
                  <View className="flex-row items-center">
                    <Ionicons name="analytics" size={20} color={theme.colors.info[500]} style={{marginRight: 8}} />
                    <Text style={{color: theme.colors.textSecondary}}>Data Points</Text>
                  </View>
                  <Text style={{fontWeight: '600', color: theme.colors.text}}>
                    {(admissionMetrics?.daily_trend?.length || 0) +
                     (revenueMetrics?.daily_trend?.length || 0) +
                     (dischargeMetrics?.daily_trend?.length || 0)} records
                  </Text>
                </View>
              </View>
            </Section>

            {/* Footer */}
            <View style={{
  backgroundColor: theme.isDark ? theme.colors.gray[800] : theme.colors.surface,
  borderRadius: 12,
  padding: 16,
  borderWidth: 1,
  borderColor: theme.isDark ? theme.colors.gray[700] : theme.colors.gray[200],
  ...shadowStyle,
}}>
              <View className="flex-row justify-center items-center">
                <Ionicons name="refresh-circle" size={18} color={theme.colors.primary[500]} style={{marginRight: 8}} />
                <Text style={{textAlign: 'center', color: theme.colors.textSecondary, fontSize: 14}}>
                  {autoRefresh ? 'Data refreshes automatically. Pull down to refresh manually.' : 'Pull down to refresh data.'}
                </Text>
              </View>
            </View>
          </View>
        )}
      </ScrollView>

      {/* Floating Action Button for Quick Actions */}
      <FloatingActionButton
        icon="refresh"
        onPress={handleRefresh}
        color="primary"
        position="bottom-right"
        disabled={isLoading}
      />
    </SafeAreaView>
  );
};

export default DashboardScreen;
