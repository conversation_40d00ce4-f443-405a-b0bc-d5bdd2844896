{"version": 3, "file": "parser.js", "sourceRoot": "", "sources": ["../src/parser.ts"], "names": [], "mappings": ";;;;;AAmLS,sBAAK;AAAE,wCAAc;AA/K9B,oEAA2D;AAO3D,4EAAgF;AAEhF,kEAA8D;AAC9D,kDAA0B;AAE1B,2CAA0C;AAE1C,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,iCAAiC,CAAC,CAAC;AAarD,SAAS,eAAe,CACtB,KAA0B,EAC1B,QAAQ,GAAG,KAAK;IAEhB,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE,CAAC;QAC/B,OAAO,QAAQ,CAAC;IAClB,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,kBAAkB,GAAG,wBAAwB,CAAC;AACpD,SAAS,MAAM,CAAC,eAAmC;IACjD,IAAI,eAAe,CAAC,GAAG,EAAE,CAAC;QACxB,OAAO,eAAe,CAAC,GAAG,CAAC,MAAM,CAAQ,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACpD,MAAM,KAAK,GAAG,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC;YACzD,IAAI,KAAK,EAAE,CAAC;gBACV,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAQ,CAAC,CAAC;YAC5B,CAAC;YAED,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAED,MAAM,MAAM,GAAG,eAAe,CAAC,MAAM,IAAI,yBAAY,CAAC,GAAG,CAAC;IAC1D,gIAAgI;IAChI,QAAQ,MAAM,EAAE,CAAC;QACf,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,KAAK,CAAC,CAAC;QACjB;YACE,OAAO,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC;AACH,CAAC;AAED,SAAS,KAAK,CACZ,IAA4B,EAC5B,OAAuB;IAEvB,OAAO,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC;AAC3C,CAAC;AAED,SAAS,cAAc,CACrB,IAA4B,EAC5B,OAA8B;IAE9B,IAAI,CAAC,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QAC5C,OAAO,GAAG,EAAE,CAAC;IACf,CAAC;SAAM,CAAC;QACN,OAAO,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC;IAC3B,CAAC;IACD,2EAA2E;IAC3E,yFAAyF;IACzF,IAAI,OAAO,CAAC,UAAU,KAAK,QAAQ,IAAI,OAAO,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;QACvE,OAAO,CAAC,UAAU,GAAG,QAAQ,CAAC;IAChC,CAAC;IACD,IAAI,OAAO,OAAO,CAAC,YAAY,KAAK,QAAQ,EAAE,CAAC;QAC7C,OAAO,CAAC,YAAY,GAAG,EAAE,CAAC;IAC5B,CAAC;IAED,MAAM,aAAa,GAAoB;QACrC,GAAG,OAAO;QACV,GAAG,EAAE,eAAe,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC;QAC9C;;;WAGG;QACH,2CAA2C,EAAE,KAAK;KACnD,CAAC;IACF,MAAM,cAAc,GAAmB;QACrC,YAAY,EAAE,OAAO,CAAC,YAAY,CAAC,YAAY;QAC/C,SAAS,EAAE,OAAO,CAAC,SAAS;QAC5B,eAAe,EAAE,OAAO,CAAC,eAAe;QACxC,GAAG,EAAE,OAAO,CAAC,GAAG;QAChB,UAAU,EAAE,OAAO,CAAC,UAAU;KAC/B,CAAC;IAEF;;;OAGG;IACH,MAAM,kCAAkC,GAAG,eAAe,CACxD,OAAO,CAAC,kCAAkC,EAC1C,IAAI,CACL,CAAC;IAEF,IAAI,CAAC,kCAAkC,EAAE,CAAC;QACxC,aAAa,CAAC,QAAQ,GAAG,KAAK,CAAC;IACjC,CAAC;IAED,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,IAAA,4CAAwB,EAAC,IAAI,EAAE,aAAa,CAAC,CAAC;IACxE,GAAG,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;IAEpC,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;QACrB,6DAA6D;QAC7D,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAC9D,IAAI,cAAc,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;YAC/B,cAAc,CAAC,GAAG,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC;YAC7C,GAAG,CAAC,gCAAgC,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC;QAC5D,CAAC;QACD,IACE,cAAc,CAAC,SAAS,KAAK,SAAS;YACtC,eAAe,CAAC,UAAU,IAAI,IAAI,EAClC,CAAC;YACD,2DAA2D;YAC3D,MAAM,OAAO,GAAG,eAAe,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAChE,cAAc,CAAC,SAAS,GAAG,OAAO,CAAC;YACnC,GAAG,CAAC,qCAAqC,EAAE,cAAc,CAAC,SAAS,CAAC,CAAC;QACvE,CAAC;QACD,IACE,cAAc,CAAC,eAAe,KAAK,SAAS;YAC5C,eAAe,CAAC,kBAAkB,IAAI,IAAI,EAC1C,CAAC;YACD,kEAAkE;YAClE,MAAM,WAAW,GAAG,eAAe,CAAC,kBAAkB;iBACnD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;iBACb,IAAI,EAAE,CAAC;YACV,cAAc,CAAC,eAAe,GAAG,WAAW,CAAC;YAC7C,GAAG,CACD,2CAA2C,EAC3C,cAAc,CAAC,eAAe,CAC/B,CAAC;QACJ,CAAC;IACH,CAAC;IAED,mDAAmD;IACnD,QAAQ,CAAC,qBAAqB,KAAK,OAAO,CAAC,qBAAqB,KAAK,IAAI,CAAC;IAC1E,QAAQ,CAAC,sBAAsB,KAAK,OAAO,CAAC,sBAAsB,KAAK,IAAI,CAAC;IAE5E,MAAM,YAAY,GAAG,IAAA,uBAAO,EAAC,GAAG,EAAE,cAAc,CAAC,CAAC;IAElD,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,YAAY,EAAE,WAAW,EAAX,0BAAW,EAAE,CAAC;AACtD,CAAC"}