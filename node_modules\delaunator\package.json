{"name": "delaunator", "version": "4.0.1", "description": "An incredibly fast JavaScript library for Delaunay triangulation of 2D points", "main": "delaunator.js", "module": "index.js", "jsdelivr": "delaunator.min.js", "unpkg": "delaunator.min.js", "dependencies": {}, "devDependencies": {"c8": "^5.0.1", "eslint": "^6.2.2", "eslint-config-mourner": "^3.0.0", "esm": "^3.2.25", "rollup": "^1.20.3", "rollup-plugin-buble": "^0.19.8", "rollup-plugin-terser": "^5.1.1", "tape": "^4.11.0"}, "repository": {"type": "git", "url": "https://github.com/mapbox/delaunator.git"}, "scripts": {"lint": "eslint index.js test/test.js bench.js rollup.config.js docs/diagrams.js", "pretest": "npm run lint", "test": "node -r esm test/test.js", "cov": "c8 node -r esm test/test.js && c8 report -r html", "bench": "node -r esm bench.js", "build": "rollup -c", "start": "rollup -cw", "prepublishOnly": "npm test && npm run build"}, "files": ["index.js", "delaunator.js", "delaunator.min.js"], "eslintConfig": {"extends": "mourner", "rules": {"no-sequences": 0}}, "keywords": ["delaunay triangulation", "computational geometry", "algorithms"], "author": "<PERSON>", "license": "ISC"}