{"version": 3, "names": ["getHeaderTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useTheme", "React", "I18nManager", "Platform", "StyleSheet", "View", "isNewBackTitleImplementation", "isSearchBarAvailableForCurrentPlatform", "ScreenStackHeaderBackButtonImage", "ScreenStackHeaderCenterView", "ScreenStackHeaderConfig", "ScreenStackHeaderLeftView", "ScreenStackHeaderRightView", "ScreenStackHeaderSearchBarView", "SearchBar", "processFonts", "HeaderConfig", "headerBackImageSource", "headerBackButtonMenuEnabled", "headerBackTitle", "headerBackTitleStyle", "headerBackTitleVisible", "headerBackVisible", "headerShadowVisible", "headerLargeStyle", "headerLargeTitle", "headerLargeTitleShadowVisible", "headerLargeTitleStyle", "headerBackground", "headerLeft", "headerRight", "headerShown", "headerStyle", "headerBlurEffect", "headerTintColor", "headerTitle", "headerTitleAlign", "headerTitleStyle", "headerTransparent", "headerSearchBarOptions", "headerTopInsetEnabled", "route", "title", "canGoBack", "colors", "tintColor", "OS", "primary", "text", "headerBackTitleStyleFlattened", "flatten", "headerLargeTitleStyleFlattened", "headerTitleStyleFlattened", "headerStyleFlattened", "headerLargeStyleFlattened", "backTitleFontFamily", "largeTitleFontFamily", "titleFontFamily", "fontFamily", "titleText", "name", "titleColor", "color", "titleFontSize", "fontSize", "titleFontWeight", "fontWeight", "headerTitleStyleSupported", "headerLeftElement", "label", "headerRightElement", "headerTitleElement", "children", "supportsHeaderSearchBar", "hasHeaderSearchBar", "Error", "backButtonInCustomView", "translucent", "backgroundColor", "card", "getConstants", "isRTL", "styles", "row", "undefined", "create", "flexDirection", "alignItems"], "sourceRoot": "../../../src", "sources": ["views/HeaderConfig.tsx"], "mappings": "AAAA,SAASA,cAAc,EAAEC,WAAW,QAAQ,4BAA4B;AACxE,SAAgBC,QAAQ,QAAQ,0BAA0B;AAC1D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SACEC,WAAW,EACXC,QAAQ,EACRC,UAAU,EAEVC,IAAI,QACC,cAAc;AACrB,SACEC,4BAA4B,EAC5BC,sCAAsC,EACtCC,gCAAgC,EAChCC,2BAA2B,EAC3BC,uBAAuB,EACvBC,yBAAyB,EACzBC,0BAA0B,EAC1BC,8BAA8B,EAC9BC,SAAS,QACJ,sBAAsB;AAG7B,SAASC,YAAY,QAAQ,iBAAiB;AAS9C,eAAe,SAASC,YAAY,OA4Bb;EAAA,IA5Bc;IACnCC,qBAAqB;IACrBC,2BAA2B;IAC3BC,eAAe;IACfC,oBAAoB;IACpBC,sBAAsB,GAAG,IAAI;IAC7BC,iBAAiB;IACjBC,mBAAmB;IACnBC,gBAAgB;IAChBC,gBAAgB;IAChBC,6BAA6B;IAC7BC,qBAAqB;IACrBC,gBAAgB;IAChBC,UAAU;IACVC,WAAW;IACXC,WAAW;IACXC,WAAW;IACXC,gBAAgB;IAChBC,eAAe;IACfC,WAAW;IACXC,gBAAgB;IAChBC,gBAAgB;IAChBC,iBAAiB;IACjBC,sBAAsB;IACtBC,qBAAqB;IACrBC,KAAK;IACLC,KAAK;IACLC;EACK,CAAC;EACN,MAAM;IAAEC;EAAO,CAAC,GAAG5C,QAAQ,EAAE;EAC7B,MAAM6C,SAAS,GACbX,eAAe,KAAK/B,QAAQ,CAAC2C,EAAE,KAAK,KAAK,GAAGF,MAAM,CAACG,OAAO,GAAGH,MAAM,CAACI,IAAI,CAAC;EAE3E,MAAMC,6BAA6B,GACjC7C,UAAU,CAAC8C,OAAO,CAAC9B,oBAAoB,CAAC,IAAI,CAAC,CAAC;EAChD,MAAM+B,8BAA8B,GAClC/C,UAAU,CAAC8C,OAAO,CAACvB,qBAAqB,CAAC,IAAI,CAAC,CAAC;EACjD,MAAMyB,yBAAyB,GAAGhD,UAAU,CAAC8C,OAAO,CAACb,gBAAgB,CAAC,IAAI,CAAC,CAAC;EAC5E,MAAMgB,oBAAoB,GAAGjD,UAAU,CAAC8C,OAAO,CAAClB,WAAW,CAAC,IAAI,CAAC,CAAC;EAClE,MAAMsB,yBAAyB,GAAGlD,UAAU,CAAC8C,OAAO,CAAC1B,gBAAgB,CAAC,IAAI,CAAC,CAAC;EAE5E,MAAM,CAAC+B,mBAAmB,EAAEC,oBAAoB,EAAEC,eAAe,CAAC,GAChE1C,YAAY,CAAC,CACXkC,6BAA6B,CAACS,UAAU,EACxCP,8BAA8B,CAACO,UAAU,EACzCN,yBAAyB,CAACM,UAAU,CACrC,CAAC;EAEJ,MAAMC,SAAS,GAAG7D,cAAc,CAAC;IAAE4C,KAAK;IAAEP;EAAY,CAAC,EAAEM,KAAK,CAACmB,IAAI,CAAC;EACpE,MAAMC,UAAU,GACdT,yBAAyB,CAACU,KAAK,IAAI5B,eAAe,IAAIU,MAAM,CAACI,IAAI;EACnE,MAAMe,aAAa,GAAGX,yBAAyB,CAACY,QAAQ;EACxD,MAAMC,eAAe,GAAGb,yBAAyB,CAACc,UAAU;EAE5D,MAAMC,yBAAoC,GAAG;IAAEL,KAAK,EAAED;EAAW,CAAC;EAElE,IAAIT,yBAAyB,CAACM,UAAU,IAAI,IAAI,EAAE;IAChDS,yBAAyB,CAACT,UAAU,GAAGN,yBAAyB,CAACM,UAAU;EAC7E;EAEA,IAAIK,aAAa,IAAI,IAAI,EAAE;IACzBI,yBAAyB,CAACH,QAAQ,GAAGD,aAAa;EACpD;EAEA,IAAIE,eAAe,IAAI,IAAI,EAAE;IAC3BE,yBAAyB,CAACD,UAAU,GAAGD,eAAe;EACxD;EAEA,MAAMG,iBAAiB,GAAGvC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAG;IACrCgB,SAAS;IACTF,SAAS;IACT0B,KAAK,EAAElD;EACT,CAAC,CAAC;EACF,MAAMmD,kBAAkB,GAAGxC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAG;IACvCe,SAAS;IACTF;EACF,CAAC,CAAC;EACF,MAAM4B,kBAAkB,GACtB,OAAOpC,WAAW,KAAK,UAAU,GAC7BA,WAAW,CAAC;IACVU,SAAS;IACT2B,QAAQ,EAAEb;EACZ,CAAC,CAAC,GACF,IAAI;EAEV,MAAMc,uBAAuB,GAC3B,OAAOlE,sCAAsC,KAAK,SAAS,GACvDA,sCAAsC;EACtC;EACAJ,QAAQ,CAAC2C,EAAE,KAAK,KAAK,IAAIhC,SAAS,IAAI,IAAI;EAEhD,MAAM4D,kBAAkB,GACtBD,uBAAuB,IAAIlC,sBAAsB,IAAI,IAAI;EAE3D,IAAIA,sBAAsB,IAAI,IAAI,IAAI,CAACkC,uBAAuB,EAAE;IAC9D,MAAM,IAAIE,KAAK,CACZ,gJAA+I,CACjJ;EACH;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMC,sBAAsB,GAAGtD,iBAAiB,GAC5C8C,iBAAiB,IAAI,IAAI,GACzBjE,QAAQ,CAAC2C,EAAE,KAAK,SAAS,IAAIyB,kBAAkB,IAAI,IAAI;EAE3D,MAAMM,WAAW,GACfjD,gBAAgB,IAAI,IAAI,IACxBU,iBAAiB;EACjB;EACC,CAACoC,kBAAkB,IAAIjD,gBAAgB,KACtCtB,QAAQ,CAAC2C,EAAE,KAAK,KAAK,IACrBR,iBAAiB,KAAK,KAAM;EAEhC,oBACE,oBAAC,uBAAuB;IACtB,sBAAsB,EAAEsC,sBAAuB;IAC/C,eAAe,EACbvB,oBAAoB,CAACyB,eAAe,KACnClD,gBAAgB,IAAI,IAAI,IAAIU,iBAAiB,GAC1C,aAAa,GACbM,MAAM,CAACmC,IAAI,CAChB;IACD,SAAS,EACPzE,4BAA4B,IAAIe,sBAAsB,GAClDF,eAAe,GACf,GACL;IACD,gBAAgB,EAAEE,sBAAuB;IACzC,mBAAmB,EAAEkC,mBAAoB;IACzC,iBAAiB,EAAEN,6BAA6B,CAACe,QAAS;IAC1D,UAAU,EAAE/B,gBAAiB;IAC7B,KAAK,EAAEY,SAAU;IACjB,SAAS,EAAE3C,WAAW,CAAC8E,YAAY,EAAE,CAACC,KAAK,GAAG,KAAK,GAAG,KAAM;IAC5D,qBAAqB,EAAE/D,2BAA2B,KAAK,KAAM;IAC7D,MAAM,EAAEa,WAAW,KAAK,KAAM;IAC9B,cAAc,EAAET,iBAAiB,KAAK,KAAM;IAC5C,UAAU,EACRC,mBAAmB,KAAK,KAAK,IAC7BK,gBAAgB,IAAI,IAAI,IACvBU,iBAAiB,IAAIf,mBAAmB,KAAK,IAC/C;IACD,UAAU,EAAEE,gBAAiB;IAC7B,yBAAyB,EAAE6B,yBAAyB,CAACwB,eAAgB;IACrE,eAAe,EAAE3B,8BAA8B,CAACW,KAAM;IACtD,oBAAoB,EAAEN,oBAAqB;IAC3C,kBAAkB,EAAEL,8BAA8B,CAACa,QAAS;IAC5D,oBAAoB,EAAEb,8BAA8B,CAACe,UAAW;IAChE,oBAAoB,EAAExC,6BAA6B,KAAK,KAAM;IAC9D,KAAK,EAAEiC,SAAU;IACjB,UAAU,EAAEE,UAAW;IACvB,eAAe,EAAEJ,eAAgB;IACjC,aAAa,EAAEM,aAAc;IAC7B,eAAe,EAAEE,eAAgB;IACjC,eAAe,EAAEzB,qBAAsB;IACvC,WAAW;IACT;IACAqC,WAAW,KAAK;EACjB,GAEA1E,QAAQ,CAAC2C,EAAE,KAAK,KAAK,gBACpB,0CACGsB,iBAAiB,IAAI,IAAI,gBACxB,oBAAC,yBAAyB,QACvBA,iBAAiB,CACQ,GAC1B,IAAI,EACPG,kBAAkB,IAAI,IAAI,gBACzB,oBAAC,2BAA2B,QACzBA,kBAAkB,CACS,GAC5B,IAAI,CACP,gBAEH,0CACGH,iBAAiB,IAAI,IAAI,IAAI,OAAOjC,WAAW,KAAK,UAAU,gBAC7D,oBAAC,yBAAyB,qBACxB,oBAAC,IAAI;IAAC,KAAK,EAAE+C,MAAM,CAACC;EAAI,GACrBf,iBAAiB,EACjBhC,gBAAgB,KAAK,QAAQ,GAC5B,OAAOD,WAAW,KAAK,UAAU,GAC/BoC,kBAAkB,gBAElB,oBAAC,WAAW;IACV,SAAS,EAAE1B,SAAU;IACrB,KAAK,EAAEsB;EAA0B,GAEhCR,SAAS,CAEb,GACC,IAAI,CACH,CACmB,GAC1B,IAAI,EACPvB,gBAAgB,KAAK,QAAQ,gBAC5B,oBAAC,2BAA2B,QACzB,OAAOD,WAAW,KAAK,UAAU,GAChCoC,kBAAkB,gBAElB,oBAAC,WAAW;IACV,SAAS,EAAE1B,SAAU;IACrB,KAAK,EAAEsB;EAA0B,GAEhCR,SAAS,CAEb,CAC2B,GAC5B,IAAI,CAEX,EACA1C,qBAAqB,KAAKmE,SAAS,gBAClC,oBAAC,gCAAgC;IAAC,MAAM,EAAEnE;EAAsB,EAAG,GACjE,IAAI,EACPqD,kBAAkB,IAAI,IAAI,gBACzB,oBAAC,0BAA0B,QACxBA,kBAAkB,CACQ,GAC3B,IAAI,EACPI,kBAAkB,gBACjB,oBAAC,8BAA8B,qBAC7B,oBAAC,SAAS,EAAKnC,sBAAsB,CAAI,CACV,GAC/B,IAAI,CACgB;AAE9B;AAEA,MAAM2C,MAAM,GAAG9E,UAAU,CAACiF,MAAM,CAAC;EAC/BF,GAAG,EAAE;IACHG,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE;EACd;AACF,CAAC,CAAC"}