{"version": 3, "file": "format.js", "sourceRoot": "", "sources": ["../../../src/rules/naming-convention-utils/format.ts"], "names": [], "mappings": ";;;AAAA,mCAA4C;AAE5C;;;;;;EAME;AAEF;;;;EAIE;AAEF,SAAS,YAAY,CAAC,IAAY;IAChC,OAAO,CACL,IAAI,CAAC,MAAM,KAAK,CAAC;QACjB,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAC3D,CAAC;AACJ,CAAC;AACD,SAAS,kBAAkB,CAAC,IAAY;IACtC,OAAO,CACL,IAAI,CAAC,MAAM,KAAK,CAAC;QACjB,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,IAAI,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CACvE,CAAC;AACJ,CAAC;AAED,SAAS,WAAW,CAAC,IAAY;IAC/B,OAAO,CACL,IAAI,CAAC,MAAM,KAAK,CAAC;QACjB,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAC3D,CAAC;AACJ,CAAC;AACD,SAAS,iBAAiB,CAAC,IAAY;IACrC,OAAO,CACL,IAAI,CAAC,MAAM,KAAK,CAAC;QACjB,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,IAAI,mBAAmB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CACxE,CAAC;AACJ,CAAC;AAED,SAAS,mBAAmB,CAAC,IAAY,EAAE,OAAgB;IACzD,SAAS,eAAe,CAAC,IAAY;QACnC,OAAO,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE,IAAI,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC;IACpE,CAAC;IAED,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QACzB,OAAO,KAAK,CAAC;IACf,CAAC;IACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;QACrC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YACpB,OAAO,KAAK,CAAC;QACf,CAAC;QACD,IAAI,OAAO,KAAK,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACzC,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,CAAC,OAAO,CAAC;QACrB,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,WAAW,CAAC,IAAY;IAC/B,OAAO,CACL,IAAI,CAAC,MAAM,KAAK,CAAC;QACjB,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAC3D,CAAC;AACJ,CAAC;AAED,SAAS,WAAW,CAAC,IAAY;IAC/B,OAAO,CACL,IAAI,CAAC,MAAM,KAAK,CAAC;QACjB,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAC3D,CAAC;AACJ,CAAC;AAED,0DAA0D;AAC1D,SAAS,mBAAmB,CAAC,IAAY;IACvC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QACzB,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,aAAa,GAAG,KAAK,CAAC;IAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;QACrC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YACpB,IAAI,aAAa,EAAE,CAAC;gBAClB,OAAO,KAAK,CAAC;YACf,CAAC;YACD,aAAa,GAAG,IAAI,CAAC;QACvB,CAAC;aAAM,CAAC;YACN,aAAa,GAAG,KAAK,CAAC;QACxB,CAAC;IACH,CAAC;IACD,OAAO,CAAC,aAAa,CAAC;AACxB,CAAC;AAED,MAAM,+BAA+B,GAEjC;IACF,CAAC,yBAAiB,CAAC,UAAU,CAAC,EAAE,YAAY;IAC5C,CAAC,yBAAiB,CAAC,gBAAgB,CAAC,EAAE,kBAAkB;IACxD,CAAC,yBAAiB,CAAC,SAAS,CAAC,EAAE,WAAW;IAC1C,CAAC,yBAAiB,CAAC,eAAe,CAAC,EAAE,iBAAiB;IACtD,CAAC,yBAAiB,CAAC,UAAU,CAAC,EAAE,WAAW;IAC3C,CAAC,yBAAiB,CAAC,UAAU,CAAC,EAAE,WAAW;CAC5C,CAAC;AAEO,0EAA+B"}