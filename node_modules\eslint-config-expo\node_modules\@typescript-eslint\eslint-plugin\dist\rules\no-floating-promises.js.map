{"version": 3, "file": "no-floating-promises.js", "sourceRoot": "", "sources": ["../../src/rules/no-floating-promises.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,sDAAwC;AACxC,+CAAiC;AAGjC,kCASiB;AAqBjB,MAAM,WAAW,GACf,4GAA4G,CAAC;AAE/G,MAAM,eAAe,GACnB,wGAAwG;IACxG,+DAA+D,CAAC;AAElE,MAAM,uBAAuB,GAC3B,6DAA6D,CAAC;AAEhE,MAAM,mBAAmB,GACvB,kIAAkI,CAAC;AAErI,MAAM,uBAAuB,GAC3B,kIAAkI;IAClI,4EAA4E,CAAC;AAE/E,kBAAe,IAAA,iBAAU,EAAqB;IAC5C,IAAI,EAAE,sBAAsB;IAC5B,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,6DAA6D;YAC/D,WAAW,EAAE,aAAa;YAC1B,oBAAoB,EAAE,IAAI;SAC3B;QACD,cAAc,EAAE,IAAI;QACpB,QAAQ,EAAE;YACR,QAAQ,EAAE,WAAW;YACrB,gBAAgB,EAAE,qBAAqB;YACvC,YAAY,EAAE,eAAe;YAC7B,eAAe,EAAE,8BAA8B;YAC/C,+BAA+B,EAC7B,WAAW,GAAG,GAAG,GAAG,uBAAuB;YAC7C,mCAAmC,EACjC,eAAe,GAAG,GAAG,GAAG,uBAAuB;YACjD,oBAAoB,EAAE,mBAAmB;YACzC,wBAAwB,EAAE,uBAAuB;SAClD;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,yBAAyB,EAAE,gCAAyB,CAAC,UAAU,CAAC,KAAK;oBACrE,cAAc,EAAE;wBACd,WAAW,EACT,uEAAuE;wBACzE,IAAI,EAAE,SAAS;qBAChB;oBACD,UAAU,EAAE;wBACV,WAAW,EAAE,uCAAuC;wBACpD,IAAI,EAAE,SAAS;qBAChB;oBACD,UAAU,EAAE;wBACV,WAAW,EACT,2EAA2E;wBAC7E,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;QACD,IAAI,EAAE,SAAS;KAChB;IACD,cAAc,EAAE;QACd;YACE,yBAAyB,EAAE,kCAA2B,CAAC,KAAK;YAC5D,cAAc,EAAE,IAAI;YACpB,UAAU,EAAE,IAAI;YAChB,UAAU,EAAE,KAAK;SAClB;KACF;IAED,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAClD,MAAM,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;QAEnC,cAAc;QACd,oEAAoE;QACpE,MAAM,yBAAyB,GAAG,OAAO,CAAC,yBAA0B,CAAC;QAErE,OAAO;YACL,mBAAmB,CAAC,IAAI;gBACtB,IAAI,OAAO,CAAC,UAAU,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC5C,OAAO;gBACT,CAAC;gBAED,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;gBAEjC,IAAI,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE,CAAC;oBACvD,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC;gBACrC,CAAC;gBAED,MAAM,EAAE,WAAW,EAAE,kBAAkB,EAAE,YAAY,EAAE,GACrD,kBAAkB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;gBAE1C,IAAI,WAAW,EAAE,CAAC;oBAChB,IAAI,YAAY,EAAE,CAAC;wBACjB,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI;4BACJ,SAAS,EAAE,OAAO,CAAC,UAAU;gCAC3B,CAAC,CAAC,0BAA0B;gCAC5B,CAAC,CAAC,sBAAsB;yBAC3B,CAAC,CAAC;oBACL,CAAC;yBAAM,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;wBAC9B,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI;4BACJ,SAAS,EAAE,kBAAkB;gCAC3B,CAAC,CAAC,qCAAqC;gCACvC,CAAC,CAAC,cAAc;4BAClB,OAAO,EAAE;gCACP;oCACE,SAAS,EAAE,iBAAiB;oCAC5B,GAAG,CAAC,KAAK;wCACP,MAAM,MAAM,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAC/C,IAAI,CAAC,UAAU,CAChB,CAAC;wCACF,IAAI,2BAA2B,CAAC,MAAM,CAAC,EAAE,CAAC;4CACxC,OAAO,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;wCAC/C,CAAC;wCACD,OAAO;4CACL,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC;4CACtC,KAAK,CAAC,oBAAoB,CACxB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAC1C,GAAG,CACJ;yCACF,CAAC;oCACJ,CAAC;iCACF;6BACF;yBACF,CAAC,CAAC;oBACL,CAAC;yBAAM,CAAC;wBACN,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI;4BACJ,SAAS,EAAE,kBAAkB;gCAC3B,CAAC,CAAC,iCAAiC;gCACnC,CAAC,CAAC,UAAU;4BACd,OAAO,EAAE;gCACP;oCACE,SAAS,EAAE,kBAAkB;oCAC7B,GAAG,CAAC,KAAK;wCACP,IACE,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;4CAClD,UAAU,CAAC,QAAQ,KAAK,MAAM,EAC9B,CAAC;4CACD,OAAO,KAAK,CAAC,gBAAgB,CAC3B,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAC9C,OAAO,CACR,CAAC;wCACJ,CAAC;wCACD,MAAM,MAAM,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAC/C,IAAI,CAAC,UAAU,CAChB,CAAC;wCACF,IAAI,2BAA2B,CAAC,MAAM,CAAC,EAAE,CAAC;4CACxC,OAAO,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;wCAChD,CAAC;wCACD,OAAO;4CACL,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC;4CACvC,KAAK,CAAC,oBAAoB,CACxB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAC1C,GAAG,CACJ;yCACF,CAAC;oCACJ,CAAC;iCACF;6BACF;yBACF,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;SACF,CAAC;QAEF,SAAS,2BAA2B,CAAC,IAAa;YAChD,MAAM,QAAQ,GAAG,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC;gBAC1C,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI;gBACzB,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC;YAC1B,MAAM,cAAc,GAAG,IAAA,4BAAqB,EAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAClE,OAAO,cAAc,GAAG,yBAAkB,CAAC,KAAK,CAAC;QACnD,CAAC;QAED,SAAS,WAAW,CAAC,IAAkC;YACrD,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EAAE,CAAC;gBAC3D,OAAO,KAAK,CAAC;YACf,CAAC;YAED,OAAO,CACL,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI;gBACzB,sBAAc,CAAC,uBAAuB;gBACxC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,CAClE,CAAC;QACJ,CAAC;QAED,SAAS,uBAAuB,CAAC,gBAA+B;YAC9D,OAAO,CACL,QAAQ,CAAC,OAAO;iBACb,cAAc,EAAE;iBAChB,iBAAiB,CAChB,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,gBAAgB,CAAC,CACrD;iBACA,iBAAiB,EAAE,CAAC,MAAM,GAAG,CAAC,CAClC,CAAC;QACJ,CAAC;QAED,SAAS,kBAAkB,CACzB,OAAuB,EACvB,IAAmB;YAMnB,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,oBAAoB,EAAE,CAAC;gBACtD,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC;YAChC,CAAC;YAED,yEAAyE;YACzE,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAAE,CAAC;gBACpD,uEAAuE;gBACvE,yEAAyE;gBACzE,yBAAyB;gBACzB,OAAO,CACL,IAAI,CAAC,WAAW;qBACb,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;qBAC9C,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,CAChE,CAAC;YACJ,CAAC;YAED,IACE,CAAC,OAAO,CAAC,UAAU;gBACnB,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBAC5C,IAAI,CAAC,QAAQ,KAAK,MAAM,EACxB,CAAC;gBACD,yEAAyE;gBACzE,4EAA4E;gBAC5E,OAAO,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpD,CAAC;YAED,MAAM,MAAM,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAExD,4EAA4E;YAC5E,oBAAoB;YAEpB,IAAI,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC3B,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;YACnD,CAAC;YAED,+DAA+D;YAC/D,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE,CAAC;gBACjD,mEAAmE;gBACnE,oEAAoE;gBACpE,sEAAsE;gBACtE,qDAAqD;gBACrD,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC;YAChC,CAAC;YAED,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC3B,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC;YAChC,CAAC;YAED,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EAAE,CAAC;gBAChD,oEAAoE;gBACpE,yCAAyC;gBAEzC,MAAM,qBAAqB,GAAG,gCAAgC,CAAC,IAAI,CAAC,CAAC;gBACrE,IAAI,qBAAqB,EAAE,CAAC;oBAC1B,IAAI,uBAAuB,CAAC,qBAAqB,CAAC,EAAE,CAAC;wBACnD,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC;oBAChC,CAAC;oBACD,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC;gBACzD,CAAC;gBAED,MAAM,oBAAoB,GAAG,+BAA+B,CAAC,IAAI,CAAC,CAAC;gBACnE,IAAI,oBAAoB,EAAE,CAAC;oBACzB,IAAI,uBAAuB,CAAC,oBAAoB,CAAC,EAAE,CAAC;wBAClD,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC;oBAChC,CAAC;oBACD,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,CAAC;gBACzD,CAAC;gBAED,2EAA2E;gBAC3E,yDAAyD;gBACzD,MAAM,oBAAoB,GAAG,wBAAwB,CAAC,IAAI,CAAC,CAAC;gBAC5D,IAAI,oBAAoB,EAAE,CAAC;oBACzB,OAAO,kBAAkB,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;gBAC3D,CAAC;gBAED,iCAAiC;gBACjC,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;YAC/B,CAAC;iBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,qBAAqB,EAAE,CAAC;gBAC9D,4EAA4E;gBAC5E,gCAAgC;gBAChC,MAAM,eAAe,GAAG,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;gBACpE,IAAI,eAAe,CAAC,WAAW,EAAE,CAAC;oBAChC,OAAO,eAAe,CAAC;gBACzB,CAAC;gBACD,OAAO,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACtD,CAAC;iBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE,CAAC;gBAC1D,MAAM,UAAU,GAAG,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1D,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;oBAC3B,OAAO,UAAU,CAAC;gBACpB,CAAC;gBACD,OAAO,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACjD,CAAC;YAED,8BAA8B;YAC9B,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;QAC/B,CAAC;QAED,SAAS,cAAc,CAAC,IAAa;YACnC,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAC7C,KAAK,MAAM,EAAE,IAAI,OAAO;iBACrB,cAAc,CAAC,IAAI,CAAC;iBACpB,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACxC,IAAI,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC;oBAC5B,MAAM,SAAS,GAAG,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClD,IAAI,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,CAAC;wBACnC,OAAO,IAAI,CAAC;oBACd,CAAC;gBACH,CAAC;gBAED,IAAI,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC;oBAC5B,KAAK,MAAM,gBAAgB,IAAI,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;wBAC5D,IAAI,aAAa,CAAC,IAAI,EAAE,gBAAgB,CAAC,EAAE,CAAC;4BAC1C,OAAO,IAAI,CAAC;wBACd,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,aAAa,CAAC,IAAa,EAAE,IAAc;YAClD,IAAI,KAAK,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAEzC,wDAAwD;YACxD,IACE,yBAAyB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAC3C,IAAA,2BAAoB,EAAC,IAAI,EAAE,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,CAC1D,EACD,CAAC;gBACD,OAAO,KAAK,CAAC;YACf,CAAC;YAED,2EAA2E;YAC3E,MAAM,SAAS,GAAG,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;YACxE,IACE,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CACxB,IAAA,0BAAmB,EAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC,CAC3D,EACD,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,wDAAwD;YACxD,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,OAAO,KAAK,CAAC;YACf,CAAC;YAED,6EAA6E;YAC7E,0EAA0E;YAC1E,EAAE;YACF,0GAA0G;YAC1G,KAAK,MAAM,EAAE,IAAI,SAAS,EAAE,CAAC;gBAC3B,MAAM,IAAI,GAAG,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;gBACpC,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;oBACvB,SAAS;gBACX,CAAC;gBAED,MAAM,QAAQ,GAAG,OAAO,CAAC,yBAAyB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAC/D,IACE,oBAAoB,CAClB,QAAQ,EACR,SAAS,CAAC,EAAE,CACV,SAAS,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC;oBAChC,eAAe,CAAC,OAAO,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;oBACvD,eAAe,CAAC,OAAO,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAC1D,EACD,CAAC;oBACD,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,oBAAoB,CAC3B,IAAa,EACb,OAA6C;IAE7C,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;QAC7C,IAAI,CAAC,CAAC,iBAAiB,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YACxC,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,eAAe,CACtB,OAAuB,EACvB,KAAgB,EAChB,IAAa;IAEb,MAAM,IAAI,GAAwB,OAAO,CAAC,eAAe,CACvD,OAAO,CAAC,yBAAyB,CAAC,KAAK,EAAE,IAAI,CAAC,CAC/C,CAAC;IACF,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;QAC7C,IAAI,CAAC,CAAC,iBAAiB,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,gCAAgC,CACvC,UAAmC;IAEnC,IACE,UAAU,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;QAC1D,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;QAC7D,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,OAAO;QAC3C,UAAU,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,EAChC,CAAC;QACD,OAAO,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,+BAA+B,CACtC,UAAmC;IAEnC,IACE,UAAU,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;QAC1D,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;QAC7D,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,MAAM;QAC1C,UAAU,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,EAChC,CAAC;QACD,OAAO,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,wBAAwB,CAC/B,UAAmC;IAEnC,OAAO,UAAU,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;QAC/D,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;QAC7D,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,SAAS;QAC7C,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM;QAC1B,CAAC,CAAC,SAAS,CAAC;AAChB,CAAC"}