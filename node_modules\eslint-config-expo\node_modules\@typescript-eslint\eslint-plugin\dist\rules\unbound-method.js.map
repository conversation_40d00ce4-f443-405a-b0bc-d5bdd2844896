{"version": 3, "file": "unbound-method.js", "sourceRoot": "", "sources": ["../../src/rules/unbound-method.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,sDAAwC;AACxC,+CAAiC;AAEjC,kCAMiB;AAcjB;;;;;;;;;;GAUG;AACH,MAAM,iBAAiB,GAAG;IACxB,QAAQ;IACR,QAAQ;IACR,QAAQ,EAAE,wEAAwE;IAClF,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,OAAO;IACP,MAAM;IACN,SAAS;IACT,SAAS;IACT,SAAS;IACT,MAAM;IACN,MAAM;IACN,MAAM;CACE,CAAC;AACX,MAAM,oBAAoB,GAAG,IAAI,GAAG,CAClC,iBAAiB,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;IACpC,IAAI,CAAC,CAAC,SAAS,IAAI,MAAM,CAAC,EAAE,CAAC;QAC3B,+EAA+E;QAC/E,qEAAqE;QACrE,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;IACjC,OAAO,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC;SACtC,MAAM,CACL,IAAI,CAAC,EAAE,CACL,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;QACrB,OAAQ,MAAkC,CAAC,IAAI,CAAC,KAAK,UAAU,CAClE;SACA,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,SAAS,IAAI,IAAI,EAAE,CAAC,CAAC;AACzC,CAAC,CAAC,CACH,CAAC;AAEF,MAAM,sBAAsB,GAAG;IAC7B,mBAAmB;IACnB,mBAAmB;IACnB,mBAAmB;IACnB,mBAAmB;IACnB,kBAAkB;IAClB,OAAO;IACP,kBAAkB;IAClB,SAAS;IACT,iBAAiB;IACjB,SAAS;IACT,MAAM;IACN,MAAM;CACP,CAAC;AAEF,MAAM,aAAa,GAAG,CACpB,MAAiB,EACjB,iBAA4C,EACnC,EAAE;IACX,MAAM,EAAE,gBAAgB,EAAE,GAAG,MAAM,CAAC;IACpC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACtB,sEAAsE;QACtE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,CACL,CAAC,CAAC,iBAAiB;QACnB,iBAAiB,KAAK,gBAAgB,CAAC,aAAa,EAAE,CACvD,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,YAAY,GAChB,oFAAoF,CAAC;AAEvF,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,gBAAgB;IACtB,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,8DAA8D;YAChE,WAAW,EAAE,aAAa;YAC1B,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,OAAO,EAAE,YAAY;YACrB,4BAA4B,EAC1B,YAAY;gBACZ,IAAI;gBACJ,8HAA8H;SACjI;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,YAAY,EAAE;wBACZ,WAAW,EACT,wEAAwE;wBAC1E,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;QACD,IAAI,EAAE,SAAS;KAChB;IACD,cAAc,EAAE;QACd;YACE,YAAY,EAAE,KAAK;SACpB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC;QAChC,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,iBAAiB,GAAG,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAE3E,SAAS,sBAAsB,CAC7B,IAAmB,EACnB,MAA6B;YAE7B,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,EAAE,SAAS,EAAE,gBAAgB,EAAE,GAAG,aAAa,CACnD,MAAM,EACN,YAAY,CACb,CAAC;YACF,IAAI,SAAS,EAAE,CAAC;gBACd,OAAO,CAAC,MAAM,CAAC;oBACb,SAAS,EACP,gBAAgB,KAAK,KAAK;wBACxB,CAAC,CAAC,8BAA8B;wBAChC,CAAC,CAAC,SAAS;oBACf,IAAI;iBACL,CAAC,CAAC;gBACH,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,eAAe,CACtB,MAAqB,EACrB,QAAuB;YAEvB,0EAA0E;YAC1E,oEAAoE;YACpE,2EAA2E;YAC3E,2EAA2E;YAC3E,iBAAiB;YACjB,EAAE;YACF,iHAAiH;YACjH,IACE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBACzC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAC3C,CAAC;gBACD,MAAM,YAAY,GAAG,QAAQ,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;gBAC1D,MAAM,WAAW,GACf,YAAY,IAAI,IAAI;oBACpB,aAAa,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;gBAEjD,IACE,WAAW;oBACX,oBAAoB,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC,EAC3D,CAAC;oBACD,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;YAED,4DAA4D;YAC5D,kEAAkE;YAClE,OAAO,CACL,IAAA,0BAAmB,EACjB,QAAQ,CAAC,OAAO,EAChB,QAAQ,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAClC,sBAAsB,CACvB;gBACD,IAAA,iCAA0B,EACxB,QAAQ,CAAC,OAAO,EAChB,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,SAAS,EAAE,CACjD,CACF,CAAC;QACJ,CAAC;QAED,OAAO;YACL,gBAAgB,CAAC,IAA+B;gBAC9C,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACnE,OAAO;gBACT,CAAC;gBAED,sBAAsB,CAAC,IAAI,EAAE,QAAQ,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;YACnE,CAAC;YACD,aAAa,CAAC,IAAI;gBAChB,IAAI,2BAA2B,CAAC,IAAI,CAAC,EAAE,CAAC;oBACtC,OAAO;gBACT,CAAC;gBACD,IAAI,QAAQ,GAAyB,IAAI,CAAC;gBAC1C,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAAE,CAAC;oBAC3D,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBAC9B,CAAC;qBAAM,IACL,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;oBACrD,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,oBAAoB,EACxD,CAAC;oBACD,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC/B,CAAC;gBAED,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;oBACvC,IACE,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,QAAQ;wBACzC,QAAQ,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAC/C,CAAC;wBACD,SAAS;oBACX,CAAC;oBAED,IAAI,QAAQ,EAAE,CAAC;wBACb,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;4BAC7C,MAAM,QAAQ,GAAG,sBAAsB,CACrC,QAAQ,CAAC,GAAG,EACZ,QAAQ;iCACL,iBAAiB,CAAC,QAAQ,CAAC;iCAC3B,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAClC,CAAC;4BACF,IAAI,QAAQ,EAAE,CAAC;gCACb,SAAS;4BACX,CAAC;4BACD,2DAA2D;4BAC3D,+DAA+D;4BAC/D,iBAAiB;4BACjB,wDAAwD;wBAC1D,CAAC;6BAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE,CAAC;4BACjE,SAAS;wBACX,CAAC;oBACH,CAAC;oBAED,KAAK,MAAM,gBAAgB,IAAI,OAAO;yBACnC,cAAc,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;yBAChD,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,OAAO,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;wBAClE,MAAM,QAAQ,GAAG,sBAAsB,CACrC,QAAQ,CAAC,GAAG,EACZ,gBAAgB,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAChD,CAAC;wBACF,IAAI,QAAQ,EAAE,CAAC;4BACb,MAAM;wBACR,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,2BAA2B,CAAC,IAAmB;IACtD,IAAI,MAAM,GAA8B,IAAI,CAAC;IAC7C,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAChC,IACE,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,IAAI,MAAM,CAAC,OAAO,CAAC;YACnE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,0BAA0B;YACzD,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;YAChD,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;YAC7C,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,sBAAsB;YACrD,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,sBAAsB;YACrD,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,IAAI,MAAM,CAAC,OAAO,CAAC,EACtE,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAOD,SAAS,aAAa,CACpB,MAAiB,EACjB,YAAqB;IAErB,MAAM,EAAE,gBAAgB,EAAE,GAAG,MAAM,CAAC;IACpC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACtB,sEAAsE;QACtE,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;IAC9B,CAAC;IAED,QAAQ,gBAAgB,CAAC,IAAI,EAAE,CAAC;QAC9B,KAAK,EAAE,CAAC,UAAU,CAAC,mBAAmB;YACpC,OAAO;gBACL,SAAS,EACN,gBAA2C,CAAC,WAAW,EAAE,IAAI;oBAC9D,EAAE,CAAC,UAAU,CAAC,kBAAkB;aACnC,CAAC;QACJ,KAAK,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,CAAC;YACtC,MAAM,QAAQ,GAAI,gBAA0C,CAAC,WAAW,CAAC;YACzE,IAAI,QAAQ,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC;gBACvD,OAAO;oBACL,SAAS,EAAE,KAAK;iBACjB,CAAC;YACJ,CAAC;YACD,OAAO,WAAW,CAAC,QAAiC,EAAE,YAAY,CAAC,CAAC;QACtE,CAAC;QACD,KAAK,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC;QACrC,KAAK,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC;YACnC,OAAO,WAAW,CAChB,gBAA6D,EAC7D,YAAY,CACb,CAAC;QACJ,CAAC;IACH,CAAC;IAED,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;AAC9B,CAAC;AAED,SAAS,WAAW,CAClB,gBAGyB,EACzB,YAAqB;IAErB,MAAM,UAAU,GAAG,gBAAgB,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrD,MAAM,gBAAgB,GACpB,UAAU,EAAE,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU;QAClD,wEAAwE;QACxE,UAAU,CAAC,IAAI,CAAC,WAAW,KAAK,MAAM,CAAC;IACzC,MAAM,aAAa,GACjB,gBAAgB,IAAI,UAAU,CAAC,IAAI,EAAE,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC;IAE1E,OAAO;QACL,SAAS,EACP,CAAC,aAAa;YACd,CAAC,CACC,YAAY;gBACZ,OAAO,CAAC,gBAAgB,CACtB,IAAA,mBAAY,EAAC,gBAAgB,CAAC,EAC9B,EAAE,CAAC,UAAU,CAAC,aAAa,CAC5B,CACF;QACH,gBAAgB;KACjB,CAAC;AACJ,CAAC;AAED,SAAS,SAAS,CAAC,IAAmB;IACpC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAE3B,QAAQ,MAAM,EAAE,IAAI,EAAE,CAAC;QACrB,KAAK,sBAAc,CAAC,WAAW,CAAC;QAChC,KAAK,sBAAc,CAAC,YAAY,CAAC;QACjC,KAAK,sBAAc,CAAC,gBAAgB,CAAC;QACrC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,gBAAgB,CAAC;QACrC,KAAK,sBAAc,CAAC,cAAc;YAChC,OAAO,IAAI,CAAC;QAEd,KAAK,sBAAc,CAAC,cAAc;YAChC,OAAO,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC;QAEhC,KAAK,sBAAc,CAAC,qBAAqB;YACvC,OAAO,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC;QAE9B,KAAK,sBAAc,CAAC,wBAAwB;YAC1C,OAAO,MAAM,CAAC,GAAG,KAAK,IAAI,CAAC;QAE7B,KAAK,sBAAc,CAAC,eAAe;YACjC,qCAAqC;YACrC,uCAAuC;YACvC,wCAAwC;YACxC,OAAO,CAAC,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAErE,KAAK,sBAAc,CAAC,gBAAgB;YAClC,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAE5E,KAAK,sBAAc,CAAC,oBAAoB;YACtC,OAAO,CACL,MAAM,CAAC,QAAQ,KAAK,GAAG;gBACvB,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI;oBACnB,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;wBAC5C,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,KAAK;wBACzC,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;wBACpD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,CAAC,CAAC,CAChE,CAAC;QAEJ,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,mBAAmB,CAAC;QACxC,KAAK,sBAAc,CAAC,cAAc,CAAC;QACnC,KAAK,sBAAc,CAAC,eAAe;YACjC,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC;QAE3B,KAAK,sBAAc,CAAC,iBAAiB;YACnC,IAAI,MAAM,CAAC,QAAQ,KAAK,IAAI,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBACrD,qEAAqE;gBACrE,OAAO,IAAI,CAAC;YACd,CAAC;YAED,oFAAoF;YACpF,0CAA0C;YAC1C,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC;IAC7B,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC"}