{"version": 3, "file": "use-unknown-in-catch-callback-variable.js", "sourceRoot": "", "sources": ["../../src/rules/use-unknown-in-catch-callback-variable.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAK1D,sDAAwC;AAGxC,kCAOiB;AAYjB,MAAM,qBAAqB,GACzB,4DAA4D,CAAC;AAE/D,kBAAe,IAAA,iBAAU,EAAiB;IACxC,IAAI,EAAE,wCAAwC;IAC9C,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,+DAA+D;YACjE,oBAAoB,EAAE,IAAI;YAC1B,WAAW,EAAE,QAAQ;SACtB;QACD,IAAI,EAAE,YAAY;QAClB,QAAQ,EAAE;YACR,UAAU,EAAE,qBAAqB;YACjC,mCAAmC,EACjC,qBAAqB,GAAG,wCAAwC;YAClE,oCAAoC,EAClC,qBAAqB;gBACrB,wEAAwE;YAC1E,oBAAoB,EAClB,qBAAqB;gBACrB,uGAAuG;YACzG,kCAAkC,EAChC,oEAAoE;YACtE,sCAAsC,EACpC,2EAA2E;YAC7E,6BAA6B,EAC3B,iDAAiD;YACnD,iCAAiC,EAC/B,mDAAmD;SACtD;QACD,OAAO,EAAE,MAAM;QACf,MAAM,EAAE,EAAE;QACV,cAAc,EAAE,IAAI;KACrB;IAED,cAAc,EAAE,EAAE;IAElB,MAAM,CAAC,OAAO;QACZ,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAElD,SAAS,oBAAoB,CAAC,IAAyB;YACrD,IACE,CAAC,CACC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBAC7C,2BAA2B,CAAC,IAAI,EAAE,OAAO,CAAC,CAC3C,EACD,CAAC;gBACD,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,YAAY,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrE,MAAM,MAAM,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACxD,OAAO,OAAO,CAAC,cAAc,CAC3B,OAAO,EACP,MAAM,EACN,OAAO,CAAC,iBAAiB,CAAC,YAAY,CAAC,CACxC,CAAC;QACJ,CAAC;QAED,SAAS,sBAAsB,CAAC,IAAa;YAC3C,KAAK,MAAM,SAAS,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrD,MAAM,cAAc,GAAG,OAAO,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;gBAClE,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAChC,qFAAqF;oBACrF,SAAS;gBACX,CAAC;gBAED,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE,CAAC;oBAC3C,MAAM,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBAClD,IAAI,CAAC,UAAU,EAAE,CAAC;wBAChB,yDAAyD;wBACzD,SAAS;oBACX,CAAC;oBAED,IAAI,cAAc,GAAG,OAAO,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;oBAEzD,MAAM,IAAI,GAAG,UAAU,CAAC,gBAAgB,CAAC;oBACzC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAA,iCAA0B,EAAC,IAAI,CAAC,EAAE,CAAC;wBACrD,IAAI,OAAO,CAAC,WAAW,CAAC,cAAc,CAAC,EAAE,CAAC;4BACxC,cAAc,GAAG,OAAO,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC/D,CAAC;6BAAM,IAAI,OAAO,CAAC,WAAW,CAAC,cAAc,CAAC,EAAE,CAAC;4BAC/C,cAAc,GAAG,OAAO,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC/D,CAAC;6BAAM,CAAC;4BACN,wEAAwE;4BACxE,OAAO,IAAI,CAAC;wBACd,CAAC;oBACH,CAAC;oBAED,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,cAAc,CAAC,EAAE,CAAC;wBACpD,OAAO,IAAI,CAAC;oBACd,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED;;;;WAIG;QACH,SAAS,kBAAkB,CACzB,IAAkD;YAElD,MAAM,QAAQ,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC1D,MAAM,cAAc,GAAG,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAC3D,OAAO,sBAAsB,CAAC,cAAc,CAAC,CAAC;QAChD,CAAC;QAED,SAAS,4BAA4B,CACnC,aAAgD;YAEhD,2EAA2E;YAC3E,4EAA4E;YAC5E,6EAA6E;YAC7E,yDAAyD;YACzD,OAAO,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC;QACtE,CAAC;QAED,SAAS,yBAAyB,CAAC,IAA4B;YAC7D,MAAM,UAAU,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAErE,MAAM,cAAc,GAAG,OAAO,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAE7D,IAAI,OAAO,CAAC,WAAW,CAAC,cAAc,CAAC,EAAE,CAAC;gBACxC,MAAM,SAAS,GAAG,OAAO,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9D,OAAO,sBAAsB,CAAC,SAAS,CAAC,CAAC;YAC3C,CAAC;YAED,IAAI,OAAO,CAAC,WAAW,CAAC,cAAc,CAAC,EAAE,CAAC;gBACxC,MAAM,SAAS,GAAG,OAAO,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACjE,IAAI,CAAC,SAAS,EAAE,CAAC;oBACf,oEAAoE;oBACpE,OAAO,KAAK,CAAC;gBACf,CAAC;gBACD,OAAO,sBAAsB,CAAC,SAAS,CAAC,CAAC;YAC3C,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAED;;;;;;WAMG;QACH,SAAS,uCAAuC,CAC9C,QAA6B;YAE7B,uEAAuE;YACvE,IACE,CAAC,CACC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB;gBACxD,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,CACpD,EACD,CAAC;gBACD,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,MAAM,oCAAoC,GAAG,IAAA,iBAAU,EACrD,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,EACrB,6EAA6E,CAC9E,CAAC;YAEF,0FAA0F;YAC1F,MAAM,kBAAkB,GACtB,oCAGC,CAAC;YACJ,MAAM,kBAAkB,GACtB,kBAAkB,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;gBAC1D,CAAC,CAAC,kBAAkB,CAAC,IAAI;gBACzB,CAAC,CAAC,kBAAkB,CAAC;YAEzB,QAAQ,kBAAkB,CAAC,IAAI,EAAE,CAAC;gBAChC,KAAK,sBAAc,CAAC,UAAU,CAAC,CAAC,CAAC;oBAC/B,MAAM,2BAA2B,GAAG,kBAAkB,CAAC,cAAc,CAAC;oBACtE,IAAI,2BAA2B,IAAI,IAAI,EAAE,CAAC;wBACxC,OAAO;4BACL,IAAI,EAAE,kBAAkB;4BACxB,OAAO,EAAE;gCACP;oCACE,SAAS,EAAE,oCAAoC;oCAC/C,GAAG,EAAE,CAAC,KAAyB,EAAsB,EAAE;wCACrD,IACE,QAAQ,CAAC,IAAI;4CACX,sBAAc,CAAC,uBAAuB;4CACxC,IAAA,+BAAwB,EAAC,QAAQ,EAAE,OAAO,CAAC,UAAU,CAAC,EACtD,CAAC;4CACD,OAAO;gDACL,KAAK,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,CAAC;gDAC/C,KAAK,CAAC,eAAe,CAAC,kBAAkB,EAAE,YAAY,CAAC;6CACxD,CAAC;wCACJ,CAAC;wCAED,OAAO;4CACL,KAAK,CAAC,eAAe,CAAC,kBAAkB,EAAE,WAAW,CAAC;yCACvD,CAAC;oCACJ,CAAC;iCACF;6BACF;yBACF,CAAC;oBACJ,CAAC;oBAED,OAAO;wBACL,IAAI,EAAE,kBAAkB;wBACxB,OAAO,EAAE;4BACP;gCACE,SAAS,EAAE,+BAA+B;gCAC1C,GAAG,EAAE,CAAC,KAAyB,EAAoB,EAAE,CACnD,KAAK,CAAC,WAAW,CAAC,2BAA2B,EAAE,WAAW,CAAC;6BAC9D;yBACF;qBACF,CAAC;gBACJ,CAAC;gBACD,KAAK,sBAAc,CAAC,YAAY,CAAC,CAAC,CAAC;oBACjC,OAAO;wBACL,IAAI,EAAE,kBAAkB;wBACxB,SAAS,EAAE,qCAAqC;qBACjD,CAAC;gBACJ,CAAC;gBACD,KAAK,sBAAc,CAAC,aAAa,CAAC,CAAC,CAAC;oBAClC,OAAO;wBACL,IAAI,EAAE,kBAAkB;wBACxB,SAAS,EAAE,sCAAsC;qBAClD,CAAC;gBACJ,CAAC;gBACD,KAAK,sBAAc,CAAC,WAAW,CAAC,CAAC,CAAC;oBAChC,MAAM,2BAA2B,GAAG,kBAAkB,CAAC,cAAc,CAAC;oBACtE,IAAI,2BAA2B,IAAI,IAAI,EAAE,CAAC;wBACxC,OAAO;4BACL,IAAI,EAAE,kBAAkB;4BACxB,OAAO,EAAE;gCACP;oCACE,SAAS,EAAE,wCAAwC;oCACnD,GAAG,EAAE,CAAC,KAAK,EAAoB,EAAE,CAC/B,KAAK,CAAC,eAAe,CAAC,kBAAkB,EAAE,aAAa,CAAC;iCAC3D;6BACF;yBACF,CAAC;oBACJ,CAAC;oBACD,OAAO;wBACL,IAAI,EAAE,kBAAkB;wBACxB,OAAO,EAAE;4BACP;gCACE,SAAS,EAAE,mCAAmC;gCAC9C,GAAG,EAAE,CAAC,KAAK,EAAoB,EAAE,CAC/B,KAAK,CAAC,WAAW,CAAC,2BAA2B,EAAE,aAAa,CAAC;6BAChE;yBACF;qBACF,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,cAAc,CAAC,IAAI;gBACjB,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;oBACtE,OAAO;gBACT,CAAC;gBAED,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAExC,2DAA2D;gBAC3D,2EAA2E;gBAC3E,IAAI,aAAa,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,EAAE,CAAC;oBACxD,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBAChC,IAAI,yBAAyB,CAAC,aAAa,CAAC,EAAE,CAAC;4BAC7C,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI,EAAE,aAAa;gCACnB,SAAS,EAAE,YAAY;6BACxB,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC;yBAAM,IAAI,4BAA4B,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;wBACxD,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI;4BACJ,SAAS,EAAE,sBAAsB;yBAClC,CAAC,CAAC;oBACL,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,wEAAwE;gBACxE,yFAAyF;gBACzF,IAAI,kBAAkB,CAAC,aAAa,CAAC,EAAE,CAAC;oBACtC,mEAAmE;oBACnE,yDAAyD;oBACzD,MAAM,SAAS,GACb,uCAAuC,CAAC,aAAa,CAAC,CAAC;oBACzD,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,aAAa;wBACnB,SAAS,EAAE,YAAY;wBACvB,GAAG,SAAS;qBACb,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH;;;;GAIG;AACH,SAAS,2BAA2B,CAClC,gBAE4C,EAC5C,KAAa,EACb,KAA+B;IAE/B,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;QAC/B,qBAAqB;QACrB,OAAO,gBAAgB,CAAC,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC;IAClD,CAAC;IAED,yBAAyB;IACzB,MAAM,iBAAiB,GAAG,IAAA,qBAAc,EAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC3E,OAAO,iBAAiB,IAAI,IAAI,IAAI,KAAK,KAAK,iBAAiB,CAAC,KAAK,CAAC;AACxE,CAAC"}