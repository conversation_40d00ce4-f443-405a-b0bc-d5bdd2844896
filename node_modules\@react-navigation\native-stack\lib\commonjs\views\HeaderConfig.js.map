{"version": 3, "names": ["HeaderConfig", "headerBackImageSource", "headerBackButtonMenuEnabled", "headerBackTitle", "headerBackTitleStyle", "headerBackTitleVisible", "headerBackVisible", "headerShadowVisible", "headerLargeStyle", "headerLargeTitle", "headerLargeTitleShadowVisible", "headerLargeTitleStyle", "headerBackground", "headerLeft", "headerRight", "headerShown", "headerStyle", "headerBlurEffect", "headerTintColor", "headerTitle", "headerTitleAlign", "headerTitleStyle", "headerTransparent", "headerSearchBarOptions", "headerTopInsetEnabled", "route", "title", "canGoBack", "colors", "useTheme", "tintColor", "Platform", "OS", "primary", "text", "headerBackTitleStyleFlattened", "StyleSheet", "flatten", "headerLargeTitleStyleFlattened", "headerTitleStyleFlattened", "headerStyleFlattened", "headerLargeStyleFlattened", "backTitleFontFamily", "largeTitleFontFamily", "titleFontFamily", "processFonts", "fontFamily", "titleText", "getHeaderTitle", "name", "titleColor", "color", "titleFontSize", "fontSize", "titleFontWeight", "fontWeight", "headerTitleStyleSupported", "headerLeftElement", "label", "headerRightElement", "headerTitleElement", "children", "supportsHeaderSearchBar", "isSearchBarAvailableForCurrentPlatform", "SearchBar", "hasHeaderSearchBar", "Error", "backButtonInCustomView", "translucent", "backgroundColor", "card", "isNewBackTitleImplementation", "I18nManager", "getConstants", "isRTL", "styles", "row", "undefined", "create", "flexDirection", "alignItems"], "sourceRoot": "../../../src", "sources": ["views/HeaderConfig.tsx"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AAOA;AAaA;AAA+C;AAAA;AAShC,SAASA,YAAY,OA4Bb;EAAA,IA5Bc;IACnCC,qBAAqB;IACrBC,2BAA2B;IAC3BC,eAAe;IACfC,oBAAoB;IACpBC,sBAAsB,GAAG,IAAI;IAC7BC,iBAAiB;IACjBC,mBAAmB;IACnBC,gBAAgB;IAChBC,gBAAgB;IAChBC,6BAA6B;IAC7BC,qBAAqB;IACrBC,gBAAgB;IAChBC,UAAU;IACVC,WAAW;IACXC,WAAW;IACXC,WAAW;IACXC,gBAAgB;IAChBC,eAAe;IACfC,WAAW;IACXC,gBAAgB;IAChBC,gBAAgB;IAChBC,iBAAiB;IACjBC,sBAAsB;IACtBC,qBAAqB;IACrBC,KAAK;IACLC,KAAK;IACLC;EACK,CAAC;EACN,MAAM;IAAEC;EAAO,CAAC,GAAG,IAAAC,gBAAQ,GAAE;EAC7B,MAAMC,SAAS,GACbZ,eAAe,KAAKa,qBAAQ,CAACC,EAAE,KAAK,KAAK,GAAGJ,MAAM,CAACK,OAAO,GAAGL,MAAM,CAACM,IAAI,CAAC;EAE3E,MAAMC,6BAA6B,GACjCC,uBAAU,CAACC,OAAO,CAACjC,oBAAoB,CAAC,IAAI,CAAC,CAAC;EAChD,MAAMkC,8BAA8B,GAClCF,uBAAU,CAACC,OAAO,CAAC1B,qBAAqB,CAAC,IAAI,CAAC,CAAC;EACjD,MAAM4B,yBAAyB,GAAGH,uBAAU,CAACC,OAAO,CAAChB,gBAAgB,CAAC,IAAI,CAAC,CAAC;EAC5E,MAAMmB,oBAAoB,GAAGJ,uBAAU,CAACC,OAAO,CAACrB,WAAW,CAAC,IAAI,CAAC,CAAC;EAClE,MAAMyB,yBAAyB,GAAGL,uBAAU,CAACC,OAAO,CAAC7B,gBAAgB,CAAC,IAAI,CAAC,CAAC;EAE5E,MAAM,CAACkC,mBAAmB,EAAEC,oBAAoB,EAAEC,eAAe,CAAC,GAChE,IAAAC,2BAAY,EAAC,CACXV,6BAA6B,CAACW,UAAU,EACxCR,8BAA8B,CAACQ,UAAU,EACzCP,yBAAyB,CAACO,UAAU,CACrC,CAAC;EAEJ,MAAMC,SAAS,GAAG,IAAAC,wBAAc,EAAC;IAAEtB,KAAK;IAAEP;EAAY,CAAC,EAAEM,KAAK,CAACwB,IAAI,CAAC;EACpE,MAAMC,UAAU,GACdX,yBAAyB,CAACY,KAAK,IAAIjC,eAAe,IAAIU,MAAM,CAACM,IAAI;EACnE,MAAMkB,aAAa,GAAGb,yBAAyB,CAACc,QAAQ;EACxD,MAAMC,eAAe,GAAGf,yBAAyB,CAACgB,UAAU;EAE5D,MAAMC,yBAAoC,GAAG;IAAEL,KAAK,EAAED;EAAW,CAAC;EAElE,IAAIX,yBAAyB,CAACO,UAAU,IAAI,IAAI,EAAE;IAChDU,yBAAyB,CAACV,UAAU,GAAGP,yBAAyB,CAACO,UAAU;EAC7E;EAEA,IAAIM,aAAa,IAAI,IAAI,EAAE;IACzBI,yBAAyB,CAACH,QAAQ,GAAGD,aAAa;EACpD;EAEA,IAAIE,eAAe,IAAI,IAAI,EAAE;IAC3BE,yBAAyB,CAACD,UAAU,GAAGD,eAAe;EACxD;EAEA,MAAMG,iBAAiB,GAAG5C,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAG;IACrCiB,SAAS;IACTH,SAAS;IACT+B,KAAK,EAAEvD;EACT,CAAC,CAAC;EACF,MAAMwD,kBAAkB,GAAG7C,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAG;IACvCgB,SAAS;IACTH;EACF,CAAC,CAAC;EACF,MAAMiC,kBAAkB,GACtB,OAAOzC,WAAW,KAAK,UAAU,GAC7BA,WAAW,CAAC;IACVW,SAAS;IACT+B,QAAQ,EAAEd;EACZ,CAAC,CAAC,GACF,IAAI;EAEV,MAAMe,uBAAuB,GAC3B,OAAOC,0DAAsC,KAAK,SAAS,GACvDA,0DAAsC;EACtC;EACAhC,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAIgC,6BAAS,IAAI,IAAI;EAEhD,MAAMC,kBAAkB,GACtBH,uBAAuB,IAAIvC,sBAAsB,IAAI,IAAI;EAE3D,IAAIA,sBAAsB,IAAI,IAAI,IAAI,CAACuC,uBAAuB,EAAE;IAC9D,MAAM,IAAII,KAAK,CACZ,gJAA+I,CACjJ;EACH;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMC,sBAAsB,GAAG7D,iBAAiB,GAC5CmD,iBAAiB,IAAI,IAAI,GACzB1B,qBAAQ,CAACC,EAAE,KAAK,SAAS,IAAI4B,kBAAkB,IAAI,IAAI;EAE3D,MAAMQ,WAAW,GACfxD,gBAAgB,IAAI,IAAI,IACxBU,iBAAiB;EACjB;EACC,CAAC2C,kBAAkB,IAAIxD,gBAAgB,KACtCsB,qBAAQ,CAACC,EAAE,KAAK,KAAK,IACrBV,iBAAiB,KAAK,KAAM;EAEhC,oBACE,oBAAC,2CAAuB;IACtB,sBAAsB,EAAE6C,sBAAuB;IAC/C,eAAe,EACb3B,oBAAoB,CAAC6B,eAAe,KACnCzD,gBAAgB,IAAI,IAAI,IAAIU,iBAAiB,GAC1C,aAAa,GACbM,MAAM,CAAC0C,IAAI,CAChB;IACD,SAAS,EACPC,gDAA4B,IAAIlE,sBAAsB,GAClDF,eAAe,GACf,GACL;IACD,gBAAgB,EAAEE,sBAAuB;IACzC,mBAAmB,EAAEqC,mBAAoB;IACzC,iBAAiB,EAAEP,6BAA6B,CAACkB,QAAS;IAC1D,UAAU,EAAEpC,gBAAiB;IAC7B,KAAK,EAAEa,SAAU;IACjB,SAAS,EAAE0C,wBAAW,CAACC,YAAY,EAAE,CAACC,KAAK,GAAG,KAAK,GAAG,KAAM;IAC5D,qBAAqB,EAAExE,2BAA2B,KAAK,KAAM;IAC7D,MAAM,EAAEa,WAAW,KAAK,KAAM;IAC9B,cAAc,EAAET,iBAAiB,KAAK,KAAM;IAC5C,UAAU,EACRC,mBAAmB,KAAK,KAAK,IAC7BK,gBAAgB,IAAI,IAAI,IACvBU,iBAAiB,IAAIf,mBAAmB,KAAK,IAC/C;IACD,UAAU,EAAEE,gBAAiB;IAC7B,yBAAyB,EAAEgC,yBAAyB,CAAC4B,eAAgB;IACrE,eAAe,EAAE/B,8BAA8B,CAACa,KAAM;IACtD,oBAAoB,EAAER,oBAAqB;IAC3C,kBAAkB,EAAEL,8BAA8B,CAACe,QAAS;IAC5D,oBAAoB,EAAEf,8BAA8B,CAACiB,UAAW;IAChE,oBAAoB,EAAE7C,6BAA6B,KAAK,KAAM;IAC9D,KAAK,EAAEqC,SAAU;IACjB,UAAU,EAAEG,UAAW;IACvB,eAAe,EAAEN,eAAgB;IACjC,aAAa,EAAEQ,aAAc;IAC7B,eAAe,EAAEE,eAAgB;IACjC,eAAe,EAAE9B,qBAAsB;IACvC,WAAW;IACT;IACA4C,WAAW,KAAK;EACjB,GAEArC,qBAAQ,CAACC,EAAE,KAAK,KAAK,gBACpB,0CACGyB,iBAAiB,IAAI,IAAI,gBACxB,oBAAC,6CAAyB,QACvBA,iBAAiB,CACQ,GAC1B,IAAI,EACPG,kBAAkB,IAAI,IAAI,gBACzB,oBAAC,+CAA2B,QACzBA,kBAAkB,CACS,GAC5B,IAAI,CACP,gBAEH,0CACGH,iBAAiB,IAAI,IAAI,IAAI,OAAOtC,WAAW,KAAK,UAAU,gBAC7D,oBAAC,6CAAyB,qBACxB,oBAAC,iBAAI;IAAC,KAAK,EAAEwD,MAAM,CAACC;EAAI,GACrBnB,iBAAiB,EACjBrC,gBAAgB,KAAK,QAAQ,GAC5B,OAAOD,WAAW,KAAK,UAAU,GAC/ByC,kBAAkB,gBAElB,oBAAC,qBAAW;IACV,SAAS,EAAE9B,SAAU;IACrB,KAAK,EAAE0B;EAA0B,GAEhCT,SAAS,CAEb,GACC,IAAI,CACH,CACmB,GAC1B,IAAI,EACP3B,gBAAgB,KAAK,QAAQ,gBAC5B,oBAAC,+CAA2B,QACzB,OAAOD,WAAW,KAAK,UAAU,GAChCyC,kBAAkB,gBAElB,oBAAC,qBAAW;IACV,SAAS,EAAE9B,SAAU;IACrB,KAAK,EAAE0B;EAA0B,GAEhCT,SAAS,CAEb,CAC2B,GAC5B,IAAI,CAEX,EACA9C,qBAAqB,KAAK4E,SAAS,gBAClC,oBAAC,oDAAgC;IAAC,MAAM,EAAE5E;EAAsB,EAAG,GACjE,IAAI,EACP0D,kBAAkB,IAAI,IAAI,gBACzB,oBAAC,8CAA0B,QACxBA,kBAAkB,CACQ,GAC3B,IAAI,EACPM,kBAAkB,gBACjB,oBAAC,kDAA8B,qBAC7B,oBAAC,6BAAS,EAAK1C,sBAAsB,CAAI,CACV,GAC/B,IAAI,CACgB;AAE9B;AAEA,MAAMoD,MAAM,GAAGvC,uBAAU,CAAC0C,MAAM,CAAC;EAC/BF,GAAG,EAAE;IACHG,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE;EACd;AACF,CAAC,CAAC"}