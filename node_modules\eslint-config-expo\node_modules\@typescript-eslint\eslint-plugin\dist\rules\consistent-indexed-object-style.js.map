{"version": 3, "file": "consistent-indexed-object-style.js", "sourceRoot": "", "sources": ["../../src/rules/consistent-indexed-object-style.ts"], "names": [], "mappings": ";;AACA,oDAAoE;AAEpE,kCAAqC;AAKrC,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,iCAAiC;IACvC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,uCAAuC;YACpD,WAAW,EAAE,WAAW;SACzB;QACD,QAAQ,EAAE;YACR,YAAY,EAAE,gDAAgD;YAC9D,oBAAoB,EAAE,gDAAgD;SACvE;QACD,OAAO,EAAE,MAAM;QACf,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,CAAC,QAAQ,EAAE,iBAAiB,CAAC;aACpC;SACF;KACF;IACD,cAAc,EAAE,CAAC,QAAQ,CAAC;IAC1B,MAAM,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC;QACpB,SAAS,YAAY,CACnB,OAA+B,EAC/B,IAA8D,EAC9D,QAAyC,EACzC,MAAc,EACd,OAAe,EACf,OAAO,GAAG,IAAI;YAEd,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzB,OAAO;YACT,CAAC;YACD,MAAM,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC;YAEzB,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAAE,CAAC;gBACpD,OAAO;YACT,CAAC;YAED,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC1C,IAAI,SAAS,EAAE,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE,CAAC;gBAClD,OAAO;YACT,CAAC;YAED,MAAM,OAAO,GAAG,SAAS,CAAC,cAAc,CAAC;YACzC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO;YACT,CAAC;YAED,MAAM,SAAS,GAAG,MAAM,CAAC,cAAc,CAAC;YACxC,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO;YACT,CAAC;YAED,IAAI,QAAQ,EAAE,CAAC;gBACb,MAAM,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBACpD,MAAM,QAAQ,GAAG,gBAAQ,CAAC,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC7D,IAAI,QAAQ,EAAE,CAAC;oBACb,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CACzC,IAAI,CAAC,EAAE,CACL,IAAI,CAAC,eAAe;wBACpB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;wBACzC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAC5C,CAAC;oBACF,IAAI,UAAU,EAAE,CAAC;wBACf,OAAO;oBACT,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI;gBACJ,SAAS,EAAE,cAAc;gBACzB,GAAG,EAAE,OAAO;oBACV,CAAC,CAAC,CAAC,KAAK,EAAoB,EAAE;wBAC1B,MAAM,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;wBAC/D,MAAM,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CACtC,SAAS,CAAC,cAAc,CACzB,CAAC;wBACF,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ;4BAC5B,CAAC,CAAC,mBAAmB,GAAG,KAAK,KAAK,IAAI;4BACtC,CAAC,CAAC,UAAU,GAAG,KAAK,KAAK,GAAG,CAAC;wBAC/B,OAAO,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,EAAE,CAAC,CAAC;oBACjE,CAAC;oBACH,CAAC,CAAC,IAAI;aACT,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,GAAG,CAAC,IAAI,KAAK,iBAAiB,IAAI;gBAChC,eAAe,CAAC,IAAI;oBAClB,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC/B,IAAI,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE,CAAC;wBAChD,OAAO;oBACT,CAAC;oBACD,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;wBAC/B,OAAO;oBACT,CAAC;oBAED,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC;oBAC1C,IAAI,MAAM,EAAE,MAAM,KAAK,CAAC,EAAE,CAAC;wBACzB,OAAO;oBACT,CAAC;oBAED,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,sBAAsB;wBACjC,GAAG,CAAC,KAAK;4BACP,MAAM,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;4BAClD,MAAM,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;4BACnD,OAAO,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,GAAG,MAAM,IAAI,IAAI,CAAC,CAAC;wBAC/D,CAAC;qBACF,CAAC,CAAC;gBACL,CAAC;aACF,CAAC;YACF,GAAG,CAAC,IAAI,KAAK,QAAQ,IAAI;gBACvB,aAAa,CAAC,IAAI;oBAChB,MAAM,MAAM,GAAG,qBAAqB,CAAC,IAAI,CAAC,CAAC;oBAC3C,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;gBACvD,CAAC;gBACD,sBAAsB,CAAC,IAAI;oBACzB,IAAI,YAAY,GAAG,EAAE,CAAC;oBAEtB,IAAI,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC;wBACvC,YAAY,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM;6BAC1C,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;6BACvC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;oBACnB,CAAC;oBAED,YAAY,CACV,IAAI,CAAC,IAAI,CAAC,IAAI,EACd,IAAI,EACJ,IAAI,CAAC,EAAE,EACP,QAAQ,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,YAAY,KAAK,EACxC,GAAG,EACH,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CACrB,CAAC;gBACJ,CAAC;aACF,CAAC;SACH,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,qBAAqB,CAC5B,IAAmB;IAEnB,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAAE,CAAC;QACxE,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,sBAAsB,EAAE,CAAC;YAC/D,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,CAAC;QACD,OAAO,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5C,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC"}