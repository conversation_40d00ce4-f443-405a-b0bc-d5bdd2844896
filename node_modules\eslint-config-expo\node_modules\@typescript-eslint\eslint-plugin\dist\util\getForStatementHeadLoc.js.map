{"version": 3, "file": "getForStatementHeadLoc.js", "sourceRoot": "", "sources": ["../../src/util/getForStatementHeadLoc.ts"], "names": [], "mappings": ";;AAkBA,wDAeC;AAhCD,wEAAmE;AAEnE;;;;;;;;;;;;;;GAcG;AACH,SAAgB,sBAAsB,CACpC,UAA+B,EAC/B,IAGyB;IAEzB,MAAM,aAAa,GAAG,IAAA,yBAAU,EAC9B,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,GAAG,CAAC,EAClE,gDAAgD,CACjD,CAAC;IACF,OAAO;QACL,KAAK,EAAE,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;QACtC,GAAG,EAAE,eAAe,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC;KAC5C,CAAC;AACJ,CAAC"}