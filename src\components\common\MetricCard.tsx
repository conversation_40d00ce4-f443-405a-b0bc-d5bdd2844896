import React from 'react';
import { View, Text, Pressable, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme, useThemedStyles } from '../../theme';
import { shadowStyle } from '../../theme/shadow';

interface MetricCardProps {
  title: string;
  value: string | number;
  delta?: string;
  deltaType?: 'positive' | 'negative' | 'neutral';
  icon?: keyof typeof Ionicons.glyphMap;
  color?: 'primary' | 'success' | 'warning' | 'error' | 'info';
  isLoading?: boolean;
  onPress?: () => void;
  subtitle?: string;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  delta,
  deltaType = 'neutral',
  icon,
  color = 'primary',
  isLoading = false,
  onPress,
  subtitle,
}) => {
  const theme = useTheme();
  const [pressed, setPressed] = React.useState(false);
  const scaleAnim = React.useRef(new Animated.Value(1)).current;

  React.useEffect(() => {
    if (onPress) {
      Animated.spring(scaleAnim, {
        toValue: pressed ? 0.98 : 1,
        useNativeDriver: true,
        friction: 8,
        tension: 100,
      }).start();
    }
  }, [pressed, scaleAnim, onPress]);

  const styles = useThemedStyles((theme) => ({
    container: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing.md,
      borderWidth: 1,
      borderColor: theme.isDark ? theme.colors.border : '#e0e0e0',
      ...shadowStyle,
    },
    title: {
      fontSize: theme.typography.fontSizes.sm,
      fontWeight: '500',
      color: theme.isDark ? theme.colors.gray[400] : theme.colors.gray[600],
    },
    value: {
      fontSize: theme.typography.fontSizes['2xl'],
      fontWeight: '700',
      color: theme.isDark ? theme.colors.gray[100] : theme.colors.gray[900],
      marginBottom: 4,
    },
    subtitle: {
      fontSize: theme.typography.fontSizes.xs,
      color: theme.colors.gray[500],
      marginBottom: 8,
    },
  }));

  const colorMap = {
    primary: theme.colors.primary[600],
    success: theme.colors.success[600],
    warning: theme.colors.warning[600],
    error: theme.colors.error[600],
    info: theme.colors.secondary[600],
  };

  const deltaColors = {
    positive: theme.isDark ? 'text-green-400' : 'text-green-600',
    negative: theme.isDark ? 'text-red-400' : 'text-red-600',
    neutral: theme.isDark ? 'text-gray-400' : 'text-gray-600',
  };

  const CardContent = () => (
    <View style={styles.container}>
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
        <Text style={styles.title}>{title}</Text>
        {icon && (
          <Ionicons name={icon} size={20} color={colorMap[color]} />
        )}
      </View>

      <Text style={styles.value}>
        {typeof value === 'number' ? value.toLocaleString() : value}
      </Text>

      {subtitle && (
        <Text style={styles.subtitle}>{subtitle}</Text>
      )}

      {delta && (
        <Text className={`text-xs font-medium ${deltaColors[deltaType]}`}>
          {delta}
        </Text>
      )}
    </View>
  );

  return onPress ? (
    <Pressable
      onPressIn={() => setPressed(true)}
      onPressOut={() => setPressed(false)}
      onPress={onPress}
      style={{ marginBottom: 12 }}
    >
      <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
        <CardContent />
      </Animated.View>
    </Pressable>
  ) : (
    <View style={{ marginBottom: 12 }}>
      <CardContent />
    </View>
  );
};

export default MetricCard;
